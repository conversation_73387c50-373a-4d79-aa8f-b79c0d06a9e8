#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "Serial.h"

int main(void)
{
	int16_t x_diff = 0;					// 存储x坐标差值
	uint8_t data_received = 0;			// 数据接收标志

	/*模块初始化*/
	OLED_Init();		//OLED初始化
	Serial_Init();		//串口初始化

	/*OLED显示初始界面*/
	OLED_Clear();
	OLED_ShowString(1, 1, "OpenMV Tracker");	// 显示标题
	OLED_ShowString(2, 1, "X_diff:");			// 显示标签
	OLED_ShowString(3, 1, "Status:");			// 显示状态标签
	OLED_ShowString(3, 8, "Waiting...");		// 显示等待状态

	while (1)
	{
		// 检查是否接收到新数据
		if (Serial_GetRxFlag())
		{
			x_diff = Serial_GetRxData();
			data_received = 1;

			// 清除之前的显示内容
			OLED_ShowString(2, 8, "        ");  // 清除x_diff数值区域
			OLED_ShowString(3, 8, "        ");  // 清除状态区域

			// 显示接收到的x坐标差值
			if (x_diff == 9999)
			{
				// 未检测到色块
				OLED_ShowString(2, 8, "No Blob");
				OLED_ShowString(3, 8, "No Target");
			}
			else
			{
				// 检测到色块，显示差值
				OLED_ShowSignedNum(2, 8, x_diff, 4);

				// 根据差值显示状态
				if (x_diff == 0)
				{
					OLED_ShowString(3, 8, "Centered");
				}
				else if (x_diff > 0)
				{
					OLED_ShowString(3, 8, "Right");
				}
				else
				{
					OLED_ShowString(3, 8, "Left");
				}
			}
		}

		// 如果长时间没有接收到数据，显示连接状态
		static uint32_t no_data_counter = 0;
		if (!data_received)
		{
			no_data_counter++;
			if (no_data_counter > 100000)  // 大约1秒
			{
				OLED_ShowString(3, 8, "No Data ");
				no_data_counter = 0;
			}
		}
		else
		{
			no_data_counter = 0;
		}

		Delay_ms(10);  // 短暂延时
	}
}
