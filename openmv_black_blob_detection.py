# OpenMV4 H7 黑色色块识别代码
# 功能：识别黑色色块，计算与视野中心的x坐标差值，并通过串口发送给STM32

import sensor, image, time, pyb
from pyb import UART

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.RGB565)    # 设置像素格式为RGB565
sensor.set_framesize(sensor.QVGA)      # 设置分辨率为QVGA (320x240)
sensor.skip_frames(time = 2000)        # 跳过前2秒的帧，让摄像头稳定
sensor.set_auto_gain(False)            # 关闭自动增益
sensor.set_auto_whitebal(False)        # 关闭自动白平衡

# 初始化串口 (UART3, 波特率115200)
# OpenMV4 H7的UART3: TX=P4, RX=P5
uart = UART(3, 115200, timeout_char=1000)

# 获取图像尺寸
img_width = sensor.width()
img_height = sensor.height()
center_x = img_width // 2  # 视野中心x坐标

# 黑色阈值 (LAB颜色空间)
# L: 亮度 (0-100), A: 绿红轴 (-128到127), B: 蓝黄轴 (-128到127)
# 黑色的LAB值大约是: L=0-30, A和B接近0
black_threshold = (0, 30, -20, 20, -20, 20)

# LED指示灯
red_led = pyb.LED(1)    # 红色LED
green_led = pyb.LED(2)  # 绿色LED
blue_led = pyb.LED(3)   # 蓝色LED

print("OpenMV黑色色块识别系统启动")
print("图像尺寸: %dx%d" % (img_width, img_height))
print("视野中心x坐标: %d" % center_x)

clock = time.clock()

while(True):
    clock.tick()
    
    # 拍摄一帧图像
    img = sensor.snapshot()
    
    # 转换为LAB颜色空间进行黑色检测
    img_lab = img.to_lab()
    
    # 查找黑色色块
    blobs = img_lab.find_blobs([black_threshold], 
                               pixels_threshold=200,    # 最小像素数
                               area_threshold=200,      # 最小面积
                               merge=True)              # 合并重叠的色块
    
    # 处理检测到的色块
    if blobs:
        # 找到最大的色块
        largest_blob = max(blobs, key=lambda b: b.pixels())
        
        # 获取色块中心坐标
        blob_center_x = largest_blob.cx()
        blob_center_y = largest_blob.cy()
        
        # 计算与视野中心的x坐标差值
        x_diff = blob_center_x - center_x
        
        # 在图像上绘制色块边框和中心点
        img.draw_rectangle(largest_blob.rect(), color=(255, 0, 0))  # 红色边框
        img.draw_cross(blob_center_x, blob_center_y, color=(0, 255, 0))  # 绿色十字
        
        # 在图像上显示信息
        img.draw_string(10, 10, "Blob: (%d,%d)" % (blob_center_x, blob_center_y), color=(255, 255, 255))
        img.draw_string(10, 30, "Center: (%d,%d)" % (center_x, img_height//2), color=(255, 255, 255))
        img.draw_string(10, 50, "X_diff: %d" % x_diff, color=(255, 255, 0))
        
        # 通过串口发送x坐标差值
        # 发送格式: "X:差值\n"
        uart_data = "X:%d\n" % x_diff
        uart.write(uart_data)
        
        # LED指示：检测到色块
        green_led.on()
        red_led.off()
        
        print("检测到黑色色块 - 中心:(%d,%d), X差值:%d" % (blob_center_x, blob_center_y, x_diff))
        
    else:
        # 没有检测到色块
        img.draw_string(10, 10, "No black blob detected", color=(255, 0, 0))
        
        # 发送无检测信号
        uart.write("X:9999\n")  # 9999表示未检测到色块
        
        # LED指示：未检测到色块
        red_led.on()
        green_led.off()
        
    # 在图像上绘制视野中心线
    img.draw_line(center_x, 0, center_x, img_height-1, color=(0, 0, 255))  # 蓝色中心线
    
    # 显示帧率
    fps = clock.fps()
    img.draw_string(10, img_height-20, "FPS: %.1f" % fps, color=(255, 255, 255))
    
    # 短暂延时
    time.sleep_ms(50)
