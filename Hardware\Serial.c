#include "stm32f10x.h"
#include "Serial.h"
#include <stdio.h>
#include <string.h>

// 串口接收相关变量
static char Serial_RxBuffer[SERIAL_RX_BUFFER_SIZE];
static uint8_t Serial_RxIndex = 0;
static uint8_t Serial_RxFlag = 0;
static int16_t Serial_RxData = 0;

/**
  * @brief  串口初始化
  * @param  无
  * @retval 无
  */
void Serial_Init(void)
{
	// 开启时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);	// 开启USART1的时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);	// 开启GPIOA的时钟
	
	// GPIO初始化
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);					// 将PA9引脚初始化为复用推挽输出
	
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);					// 将PA10引脚初始化为上拉输入
	
	// USART初始化
	USART_InitTypeDef USART_InitStructure;
	USART_InitStructure.USART_BaudRate = 115200;
	USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
	USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
	USART_InitStructure.USART_Parity = USART_Parity_No;
	USART_InitStructure.USART_StopBits = USART_StopBits_1;
	USART_InitStructure.USART_WordLength = USART_WordLength_8b;
	USART_Init(USART1, &USART_InitStructure);				// 将USART1初始化为指定参数
	
	// 中断初始化
	USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);			// 开启串口接收数据的中断
	
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);			// 配置NVIC为分组2
	
	NVIC_InitTypeDef NVIC_InitStructure;
	NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
	NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
	NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
	NVIC_Init(&NVIC_InitStructure);							// 将NVIC初始化为指定参数
	
	USART_Cmd(USART1, ENABLE);								// 使能USART1，串口开始运行
}

/**
  * @brief  串口发送一个字节
  * @param  Byte 要发送的一个字节
  * @retval 无
  */
void Serial_SendByte(uint8_t Byte)
{
	USART_SendData(USART1, Byte);		// 将字节数据写入数据寄存器，写入后USART自动生成时序波形
	while (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);	// 等待发送完成
}

/**
  * @brief  串口发送一个字符串
  * @param  String 要发送的字符串
  * @retval 无
  */
void Serial_SendString(char *String)
{
	uint8_t i;
	for (i = 0; String[i] != '\0'; i ++)		// 遍历字符数组（字符串），遇到字符串结束标志位后停止
	{
		Serial_SendByte(String[i]);				// 依次调用Serial_SendByte发送每个字符
	}
}

/**
  * @brief  获取串口接收标志位
  * @param  无
  * @retval 串口接收标志位，范围：0~1，接收到数据后，标志位置1，读取后标志位自动清零
  */
uint8_t Serial_GetRxFlag(void)
{
	if (Serial_RxFlag == 1)			// 如果标志位为1
	{
		Serial_RxFlag = 0;			// 则将标志位清零
		return 1;					// 并且返回1
	}
	return 0;						// 如果标志位为0，则返回0
}

/**
  * @brief  获取串口接收的数据
  * @param  无
  * @retval 接收的数据，范围：-32768~32767
  */
int16_t Serial_GetRxData(void)
{
	return Serial_RxData;			// 返回接收的数据变量
}

/**
  * @brief  解析接收到的字符串
  * @param  无
  * @retval 无
  */
static void Serial_ParseRxData(void)
{
	// 查找 "X:" 标识符
	char *ptr = strstr(Serial_RxBuffer, "X:");
	if (ptr != NULL)
	{
		// 解析数值
		int value = 0;
		if (sscanf(ptr + 2, "%d", &value) == 1)
		{
			Serial_RxData = (int16_t)value;
			Serial_RxFlag = 1;  // 设置接收标志
		}
	}
}

/**
  * @brief  USART1中断函数
  * @param  无
  * @retval 无
  */
void USART1_IRQHandler(void)
{
	if (USART_GetITStatus(USART1, USART_IT_RXNE) == SET)		// 判断是否是USART1的接收事件触发的中断
	{
		uint8_t RxData = USART_ReceiveData(USART1);				// 读取数据寄存器，存放在接收的数据变量里
		
		// 将接收到的字符存入缓冲区
		if (RxData == '\n' || RxData == '\r')  // 接收到换行符
		{
			Serial_RxBuffer[Serial_RxIndex] = '\0';  // 字符串结束符
			Serial_ParseRxData();  // 解析数据
			Serial_RxIndex = 0;    // 重置索引
		}
		else if (Serial_RxIndex < SERIAL_RX_BUFFER_SIZE - 1)
		{
			Serial_RxBuffer[Serial_RxIndex] = RxData;
			Serial_RxIndex++;
		}
		else
		{
			// 缓冲区溢出，重置
			Serial_RxIndex = 0;
		}
		
		USART_ClearITPendingBit(USART1, USART_IT_RXNE);		// 清除标志位
	}
}
