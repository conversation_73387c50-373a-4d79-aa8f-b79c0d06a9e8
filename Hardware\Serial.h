#ifndef __SERIAL_H
#define __SERIAL_H

#include "stm32f10x.h"

// 串口接收缓冲区大小
#define SERIAL_RX_BUFFER_SIZE 64

// 外部变量声明（供中断函数使用）
extern char Serial_RxBuffer[SERIAL_RX_BUFFER_SIZE];
extern uint8_t Serial_RxIndex;
extern uint8_t Serial_RxFlag;
extern int16_t Serial_RxData;

// 函数声明
void Serial_Init(void);
void Serial_SendByte(uint8_t Byte);
void Serial_SendString(char *String);
uint8_t Serial_GetRxFlag(void);
int16_t Serial_GetRxData(void);
void Serial_ParseRxData(void);  // 供中断函数调用

#endif
