# OpenMV与STM32黑色色块追踪系统 - 项目文件清单

## 已创建/修改的文件

### 1. OpenMV端文件
- `openmv_black_blob_detection.py` - OpenMV黑色色块识别主程序

### 2. STM32端新增文件
- `Hardware/Serial.c` - 串口驱动实现
- `Hardware/Serial.h` - 串口驱动头文件

### 3. STM32端修改文件
- `User/main.c` - 主程序，添加了串口接收和OLED显示功能
- `User/stm32f10x_it.c` - 中断服务程序，添加了USART1中断处理

### 4. 文档文件
- `连接说明和使用指南.md` - 详细的连接和使用说明
- `项目文件清单和编译说明.md` - 本文件

## 编译前检查清单

### 1. 确保项目包含所有必要文件
- [ ] Hardware/Serial.c 已添加到项目的Hardware组
- [ ] Hardware/Serial.h 已添加到项目的Hardware组
- [ ] 所有头文件路径正确

### 2. 检查包含关系
- [ ] main.c 包含了 "Serial.h"
- [ ] stm32f10x_it.c 包含了 "Serial.h"

### 3. 编译设置
- [ ] 确保编译器能找到所有头文件
- [ ] 确保链接器包含所有源文件

## 功能验证步骤

### 1. 硬件连接验证
```
连接检查：
□ OpenMV P4 → STM32 PA10
□ OpenMV P5 → STM32 PA9  
□ OpenMV GND → STM32 GND
□ OLED SCL → STM32 PB8
□ OLED SDA → STM32 PB9
□ OLED VCC → 3.3V
□ OLED GND → GND
```

### 2. 软件功能验证

#### STM32端验证：
1. 编译并下载程序到STM32
2. 上电后OLED应显示：
   ```
   OpenMV Tracker
   X_diff:
   Status: Waiting...
   ```

#### OpenMV端验证：
1. 将openmv_black_blob_detection.py复制到OpenMV
2. 运行程序，应看到：
   - 摄像头图像正常显示
   - 串口初始化成功的消息
   - LED指示灯工作（红灯表示未检测到，绿灯表示检测到）

#### 通信验证：
1. 在OpenMV前放置黑色物体
2. STM32的OLED应显示：
   - X_diff: 具体数值（正数表示右偏，负数表示左偏）
   - Status: Left/Right/Centered
3. 移除黑色物体，OLED应显示：
   - X_diff: No Blob
   - Status: No Target

## 常见编译问题及解决方案

### 1. 找不到Serial.h
**问题**：编译时提示找不到Serial.h文件
**解决**：确保Hardware/Serial.h已正确添加到项目中

### 2. 重复定义USART1_IRQHandler
**问题**：链接时提示USART1_IRQHandler重复定义
**解决**：确保只在stm32f10x_it.c中定义了该函数

### 3. 未定义的引用
**问题**：链接时提示Serial_xxx函数未定义
**解决**：确保Hardware/Serial.c已添加到项目并参与编译

## 性能参数

### 通信参数
- 波特率：115200 bps
- 数据传输延迟：< 100ms
- 检测帧率：约20 FPS

### 检测参数
- 图像分辨率：320x240 (QVGA)
- 检测精度：±1像素
- 最小检测面积：200像素

## 调试建议

### 1. 使用串口调试
可以在STM32端添加调试输出：
```c
// 在main.c中添加调试信息
Serial_SendString("Received: ");
Serial_SendString(buffer);
Serial_SendString("\r\n");
```

### 2. 使用LED指示
可以添加LED指示来显示通信状态：
```c
// 接收到数据时点亮LED
GPIO_WriteBit(GPIOC, GPIO_Pin_13, Bit_RESET);
```

### 3. OpenMV调试
在OpenMV IDE中查看：
- 串口发送的数据
- 检测到的色块信息
- 帧率和性能指标

## 扩展功能建议

1. **多色块检测**：修改阈值参数支持多种颜色
2. **PID控制**：根据x差值实现自动跟踪控制
3. **数据记录**：记录检测数据用于分析
4. **无线通信**：使用WiFi或蓝牙替代串口通信
