# OpenMV与STM32黑色色块追踪系统

## 硬件连接

### OpenMV4 H7 与 STM32F103C8 连接方式：

```
OpenMV4 H7          STM32F103C8
---------           -----------
P4 (UART3_TX)   →   PA10 (USART1_RX)
P5 (UART3_RX)   ←   PA9  (USART1_TX)
GND             →   GND
VCC (3.3V)      →   3.3V (可选，如果OpenMV需要外部供电)
```

### OLED显示屏连接：

```
OLED显示屏          STM32F103C8
---------           -----------
SCL             →   PB8
SDA             →   PB9
VCC             →   3.3V
GND             →   GND
```

## 软件配置

### 1. OpenMV端配置

- 将 `openmv_black_blob_detection.py` 文件复制到OpenMV的SD卡或内部存储
- 通过OpenMV IDE运行代码
- 代码会自动初始化摄像头和串口通信

### 2. STM32端配置

- 确保项目中包含了以下文件：
  - `Hardware/Serial.c`
  - `Hardware/Serial.h`
  - 修改后的 `User/main.c`

## 通信协议

### 数据格式：
- OpenMV发送格式：`X:差值\n`
- 例如：
  - `X:50\n` - 色块在中心右侧50像素
  - `X:-30\n` - 色块在中心左侧30像素
  - `X:0\n` - 色块在中心位置
  - `X:9999\n` - 未检测到色块

### 串口参数：
- 波特率：115200
- 数据位：8
- 停止位：1
- 校验位：无

## 功能说明

### OpenMV端功能：
1. 实时捕获图像
2. 识别黑色色块（可调整阈值）
3. 计算色块中心与图像中心的x坐标差值
4. 通过串口发送差值数据
5. LED指示灯显示检测状态：
   - 绿灯：检测到色块
   - 红灯：未检测到色块

### STM32端功能：
1. 接收OpenMV发送的数据
2. 解析x坐标差值
3. 在OLED上显示：
   - 标题："OpenMV Tracker"
   - x坐标差值
   - 状态信息（Left/Right/Centered/No Target）

## 调试和故障排除

### 1. 检查硬件连接
- 确保OpenMV和STM32的GND相连
- 确保TX/RX交叉连接正确
- 检查OLED连接是否正确

### 2. 检查串口通信
- 使用串口调试工具监听STM32的USART1
- 检查OpenMV是否正常发送数据

### 3. 调整检测参数
如果检测效果不好，可以调整OpenMV代码中的参数：

```python
# 黑色阈值调整（在openmv_black_blob_detection.py中）
black_threshold = (0, 30, -20, 20, -20, 20)  # 可以调整这些值

# 最小检测面积调整
pixels_threshold=200    # 最小像素数
area_threshold=200      # 最小面积
```

### 4. 常见问题
- **OLED显示"No Data"**：检查串口连接和波特率设置
- **检测不到黑色物体**：调整黑色阈值参数
- **检测到太多干扰**：增加最小面积阈值

## 扩展功能

可以根据需要添加以下功能：
1. 多色块检测
2. 色块大小信息传输
3. 自动跟踪控制
4. 数据记录和分析

## 注意事项

1. 确保OpenMV和STM32使用相同的波特率
2. 注意电平匹配（都是3.3V系统）
3. 良好的光照条件有助于提高检测精度
4. 定期校准和调整检测参数
